import Image from "next/image"
import Link from "next/link"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"

export default function TechnologyPage() {
  return (
    <div className="min-h-screen">
      {/* Page Header */}
      <div className="relative w-full h-[300px] overflow-hidden">
        <Image
          src="images/technology/t1.jpg"
          alt="製造技術"
          fill
          priority
          className="object-cover"
        />
        <div className="absolute inset-0 bg-black/50 flex items-center">
          <div className="container-taiyo">
            <h1 className="text-3xl md:text-4xl font-bold text-white">製造技術</h1>
          </div>
        </div>
      </div>

      {/* Breadcrumb */}
      <div className="bg-white py-3 border-b border-gray-200">
        <div className="container-taiyo">
          <nav className="flex text-sm">
            <Link href="/" className="text-gray-500 hover:text-primary transition-colors">
              HOME
            </Link>
            <span className="mx-2 text-gray-400">&gt;</span>
            <span className="text-gray-700">製造技術</span>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="py-12 bg-white">
        <div className="container-taiyo">
          <h2 className="text-2xl font-bold text-center mb-8">様々なご要望に対応する多彩な技術</h2>
          <p className="text-center text-gray-700 mb-12 max-w-3xl mx-auto">
            創業より50年の歴史の中に、サプライヤー400社以上・幅広い工法を経験し、
            大量から少量まで幅広く生産体制を整えています。
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16">
            {/* Technology Card 1 */}
            <Card className="border border-gray-200 overflow-hidden">
              <div className="h-48 relative">
                <Image
                  src="https://www.taiyoparts.co.jp/wp/wp-content/uploads/2022/05/diecast.jpg"
                  alt="ダイカスト"
                  fill
                  className="object-cover"
                />
              </div>
              <CardHeader>
                <CardTitle>ダイカスト</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-700">
                  溶かした金属を金型に高圧注入し冷却固化する鋳造法。
                  複雑で精密な形状の部品を大量生産できます。特に軽量なアルミニウム合金やマグネシウム合金の部品製造に適しています。
                </CardDescription>
              </CardContent>
              <CardFooter>
                <Link
                  href="/technology/diecast"
                  className="text-sm text-taiyo-red hover:underline flex items-center"
                >
                  詳しく見る
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="ml-1"><path d="m9 18 6-6-6-6"></path></svg>
                </Link>
              </CardFooter>
            </Card>

            {/* Technology Card 2 */}
            <Card className="border border-gray-200 overflow-hidden">
              <div className="h-48 relative">
                <Image
                  src="https://www.taiyoparts.co.jp/wp/wp-content/uploads/2022/05/aluminum.jpg"
                  alt="アルミ押し出し"
                  fill
                  className="object-cover"
                />
              </div>
              <CardHeader>
                <CardTitle>アルミ押し出し</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-700">
                  柔らかく加熱したアルミニウムを金型に押し出して成形する方法。
                  一定の断面形状を持った長尺のアルミ製品を作ることができます。様々な産業用フレームやプロファイルの製造に適しています。
                </CardDescription>
              </CardContent>
              <CardFooter>
                <Link
                  href="/technology/aluminum"
                  className="text-sm text-taiyo-red hover:underline flex items-center"
                >
                  詳しく見る
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="ml-1"><path d="m9 18 6-6-6-6"></path></svg>
                </Link>
              </CardFooter>
            </Card>

            {/* Technology Card 3 */}
            <Card className="border border-gray-200 overflow-hidden">
              <div className="h-48 relative">
                <Image
                  src="https://www.taiyoparts.co.jp/wp/wp-content/uploads/2022/05/lostwax.jpg"
                  alt="ロストワックス"
                  fill
                  className="object-cover"
                />
              </div>
              <CardHeader>
                <CardTitle>ロストワックス</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-700">
                  精密鋳造法の一種で、蝋（ワックス）で作った原型を石膏などで包み、原型を溶かして型を作る方法。
                  非常に精密な金属部品を製造でき、複雑な形状や薄肉構造も実現できます。
                </CardDescription>
              </CardContent>
              <CardFooter>
                <Link
                  href="/technology/lost_wax"
                  className="text-sm text-taiyo-red hover:underline flex items-center"
                >
                  詳しく見る
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="ml-1"><path d="m9 18 6-6-6-6"></path></svg>
                </Link>
              </CardFooter>
            </Card>

            {/* Technology Card 4 */}
            <Card className="border border-gray-200 overflow-hidden">
              <div className="h-48 relative">
                <Image
                  src="https://www.taiyoparts.co.jp/wp/wp-content/uploads/2022/05/sand-casting.jpg"
                  alt="砂型鋳造"
                  fill
                  className="object-cover"
                />
              </div>
              <CardHeader>
                <CardTitle>砂型鋳造</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-700">
                  砂を固めて作った型に溶かした金属を流し込む伝統的な鋳造法。
                  大型の部品や少量生産に適しており、比較的低コストで様々な金属材料に対応できる柔軟性があります。
                </CardDescription>
              </CardContent>
              <CardFooter>
                <Link
                  href="/technology/sand_casting"
                  className="text-sm text-taiyo-red hover:underline flex items-center"
                >
                  詳しく見る
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="ml-1"><path d="m9 18 6-6-6-6"></path></svg>
                </Link>
              </CardFooter>
            </Card>

            {/* Technology Card 5 */}
            <Card className="border border-gray-200 overflow-hidden">
              <div className="h-48 relative">
                <Image
                  src="https://www.taiyoparts.co.jp/wp/wp-content/uploads/2022/05/surface-treatment.jpg"
                  alt="表面処理"
                  fill
                  className="object-cover"
                />
              </div>
              <CardHeader>
                <CardTitle>表面処理</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-700">
                  金属部品の外観や機能を向上させるための様々な処理技術。
                  アルマイト、メッキ、塗装、ショットブラストなど多様な方法で耐食性、耐摩耗性、美観などの特性を付与します。
                </CardDescription>
              </CardContent>
              <CardFooter>
                <Link
                  href="/technology/surface_treatment"
                  className="text-sm text-taiyo-red hover:underline flex items-center"
                >
                  詳しく見る
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="ml-1"><path d="m9 18 6-6-6-6"></path></svg>
                </Link>
              </CardFooter>
            </Card>

            {/* Technology Card 6 */}
            <Card className="border border-gray-200 overflow-hidden">
              <div className="h-48 relative">
                <Image
                  src="https://www.taiyoparts.co.jp/wp/wp-content/uploads/2022/05/unit.jpg"
                  alt="ユニット組立"
                  fill
                  className="object-cover"
                />
              </div>
              <CardHeader>
                <CardTitle>ユニット組立</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-700">
                  複数の部品を組み合わせて機能する一つのユニットを作り上げる技術。
                  設計から部品調達、組立、検査まで一貫して行い、完成品としての機能を保証します。
                </CardDescription>
              </CardContent>
              <CardFooter>
                <Link
                  href="/technology/unit"
                  className="text-sm text-taiyo-red hover:underline flex items-center"
                >
                  詳しく見る
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="ml-1"><path d="m9 18 6-6-6-6"></path></svg>
                </Link>
              </CardFooter>
            </Card>
          </div>

          <div className="bg-[#f7f7f7] p-6 md:p-8 rounded-md mb-16">
            <h3 className="text-xl font-bold mb-6">その他の加工技術</h3>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
              <Link href="/technology/cutting" className="bg-white p-4 rounded-sm border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                <p className="font-medium text-center">切削加工</p>
              </Link>
              <Link href="/technology/press" className="bg-white p-4 rounded-sm border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                <p className="font-medium text-center">プレス加工</p>
              </Link>
              <Link href="/technology/sheet_metal" className="bg-white p-4 rounded-sm border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                <p className="font-medium text-center">板金加工</p>
              </Link>
              <Link href="/technology/welding" className="bg-white p-4 rounded-sm border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                <p className="font-medium text-center">溶接加工</p>
              </Link>
              <Link href="/technology/pipe_processing" className="bg-white p-4 rounded-sm border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                <p className="font-medium text-center">パイプ加工</p>
              </Link>
              <Link href="/technology/wire_processing" className="bg-white p-4 rounded-sm border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                <p className="font-medium text-center">ワイヤー加工</p>
              </Link>
              <Link href="/technology/resin_molded_product" className="bg-white p-4 rounded-sm border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                <p className="font-medium text-center">樹脂成形品</p>
              </Link>
              <Link href="/technology/resin_cutting" className="bg-white p-4 rounded-sm border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                <p className="font-medium text-center">樹脂切削</p>
              </Link>
            </div>
          </div>

          <div className="bg-taiyo-red text-white p-6 md:p-8 rounded-md">
            <div className="flex flex-col md:flex-row gap-6 items-center">
              <div className="md:w-2/3">
                <h3 className="text-xl font-bold mb-4">カセットダイカストシステム</h3>
                <p className="mb-4">
                  華日パーツでは小ロットの製品に対応できるように、独自の「カセットダイカストシステム」を開発しています。
                  従来のダイカストと違い、簡易金型を改良して作り上げ、金型を簡素化しつつロットに合わせた方法でご提案しています。
                </p>
                <Link
                  href="/diecast-navi"
                  className="inline-flex items-center bg-white text-taiyo-red px-4 py-2 rounded-sm hover:bg-gray-100 transition-colors"
                >
                  ダイカストNaviサイトで詳しく見る
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="ml-1"><path d="m9 18 6-6-6-6"></path></svg>
                </Link>
              </div>
              <div className="md:w-1/3">
                <Image
                  src="https://www.taiyoparts.co.jp/wp/wp-content/themes/taiyoparts/images/common/casting_navi.png"
                  alt="Casting Navi"
                  width={220}
                  height={80}
                  className="mx-auto"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
