import Link from "next/link"
import Image from "next/image"

export function FeatureSection() {
  return (
    <div className="bg-white">
      {/* Red background section with main heading */}
      <div className="bg-taiyo-red py-12">
        <div className="container-taiyo">
          <div className="text-center">
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              私たちの強み
            </h2>
            <p className="text-lg text-white max-w-4xl mx-auto leading-relaxed">
              Ⅰ 一括サービス  Ⅱ 独自の開発能力  Ⅲ 開発期間が短い<br />
              Ⅳ ダイカスト技術の特長－真空ダイカスト技術   Ⅴ チームの対応スピードが速い   Ⅵ 日本国内に合資会社あり。
            </p>
          </div>
        </div>
      </div>

      {/* Three main capability boxes with background images */}
      <div className="relative">
        <div className="grid grid-cols-1 md:grid-cols-3">
          {/* 提案力 */}
          <Link href="/services" className="group relative block">
            <div className="relative h-80 overflow-hidden">
              <Image
                src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1926&q=80"
                alt="提案力"
                fill
                className="object-cover transition-transform duration-300 group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-black bg-opacity-50 group-hover:bg-opacity-40 transition-all duration-300"></div>
              <div className="absolute inset-0 flex flex-col justify-center items-center text-white p-8">
                <h3 className="text-3xl font-bold mb-6">提案力</h3>
                <p className="text-base text-center leading-relaxed mb-8">
                  幅広い専門知識から解決に導く製法の<br />
                  最適化でコストダウンを実現します。
                </p>
                <div className="w-12 h-12 rounded-full border-2 border-white flex items-center justify-center group-hover:bg-white group-hover:text-black transition-all duration-300">
                  <span className="text-xl">→</span>
                </div>
              </div>
            </div>
          </Link>

          {/* 製造力 */}
          <Link href="/manufacturer_function" className="group relative block">
            <div className="relative h-80 overflow-hidden">
              <Image
                src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                alt="製造力"
                fill
                className="object-cover transition-transform duration-300 group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-black bg-opacity-50 group-hover:bg-opacity-40 transition-all duration-300"></div>
              <div className="absolute inset-0 flex flex-col justify-center items-center text-white p-8">
                <h3 className="text-3xl font-bold mb-6">製造力</h3>
                <p className="text-base text-center leading-relaxed mb-8">
                  ① 信頼性の高い生産システム、ERP生産管理システムを備えています<br />
                  ② 厳格な品質管理を行い、厳密な製品検査は製品合格率を確保する重要な手段です。また、一流の検査設備は製品品質を確実に保証する有効なツールであり、ゼロ欠陥を目指し、高品質を追求しています。
                </p>
                <div className="w-12 h-12 rounded-full border-2 border-white flex items-center justify-center group-hover:bg-white group-hover:text-black transition-all duration-300">
                  <span className="text-xl">→</span>
                </div>
              </div>
            </div>
          </Link>

          {/* 調達力 */}
          <Link href="/technology" className="group relative block">
            <div className="relative h-80 overflow-hidden">
              <Image
                src="https://images.unsplash.com/photo-1504328345606-18bbc8c9d7d1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                alt="調達力"
                fill
                className="object-cover transition-transform duration-300 group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-black bg-opacity-50 group-hover:bg-opacity-40 transition-all duration-300"></div>
              <div className="absolute inset-0 flex flex-col justify-center items-center text-white p-8">
                <h3 className="text-3xl font-bold mb-6">調達力</h3>
                <p className="text-base text-center leading-relaxed mb-8">
                  ダイカストカセットシステムを始め、<br />
                  豊富な技術でワンストップショッピング<br />
                  を可能にします。
                </p>
                <div className="w-12 h-12 rounded-full border-2 border-white flex items-center justify-center group-hover:bg-white group-hover:text-black transition-all duration-300">
                  <span className="text-xl">→</span>
                </div>
              </div>
            </div>
          </Link>
        </div>
      </div>

      {/* Technology showcase section */}
      <div className="py-16">
        <div className="container-taiyo">
          <div className="text-center mb-12">
          <h2 className="text-2xl sm:text-3xl font-bold mb-6">様々なご要望にお応えする多彩な技術</h2>
          <p className="text-base text-gray-700 mb-8 max-w-4xl mx-auto leading-relaxed">
            本社と10箇所の工場中心に、サプライヤー400社以上・海外の工場を保有し<br />
            充実した設備と多彩な加工技術でご要望にお応えしています。
          </p>

          {/* Featured technologies - 4 main ones */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
            <Link href="/technology/diecast" className="group">
              <div className="bg-white border-2 border-gray-200 rounded-lg p-8 hover:shadow-xl hover:border-taiyo-red transition-all duration-300">
                <Image
                  src="/images/products/p1.png"
                  alt="ダイカスト"
                  width={220}
                  height={140}
                  className="mx-auto mb-6 h-auto group-hover:scale-105 transition-transform duration-300"
                />
                <h3 className="text-xl font-bold text-center">ダイカスト</h3>
              </div>
            </Link>

            <Link href="/technology/aluminum" className="group">
              <div className="bg-white border-2 border-gray-200 rounded-lg p-8 hover:shadow-xl hover:border-taiyo-red transition-all duration-300">
                <Image
                  src="/images/products/p2.png"
                  alt="アルミ押し出し"
                  width={220}
                  height={140}
                  className="mx-auto mb-6 h-auto group-hover:scale-105 transition-transform duration-300"
                />
                <h3 className="text-xl font-bold text-center">アルミ押し出し</h3>
              </div>
            </Link>

            <Link href="/technology/unit" className="group">
              <div className="bg-white border-2 border-gray-200 rounded-lg p-8 hover:shadow-xl hover:border-taiyo-red transition-all duration-300">
                <Image
                  src="/images/products/p5.png"
                  alt="ユニット組立"
                  width={220}
                  height={140}
                  className="mx-auto mb-6 h-auto group-hover:scale-105 transition-transform duration-300"
                />
                <h3 className="text-xl font-bold text-center">ユニット組立</h3>
              </div>
            </Link>

            <Link href="/services/development" className="group">
              <div className="bg-white border-2 border-gray-200 rounded-lg p-8 hover:shadow-xl hover:border-taiyo-red transition-all duration-300">
                <Image
                  src="/images/products/p4.png"
                  alt="商品設計・開発"
                  width={220}
                  height={140}
                  className="mx-auto mb-6 h-auto group-hover:scale-105 transition-transform duration-300"
                />
                <h3 className="text-xl font-bold text-center">商品設計・開発</h3>
              </div>
            </Link>
          </div>

          <Link
            href="/technology"
            className="inline-flex items-center bg-taiyo-red text-white px-12 py-5 rounded-sm hover:bg-taiyo-red/90 transition-colors font-bold text-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
          >
            この他の技術一覧はこちら
          </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
