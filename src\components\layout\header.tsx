"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { She<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, SheetTrigger } from "@/components/ui/sheet"
import { Menu, Phone, Mail, User } from "lucide-react"

export function Header() {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <header className="w-full fixed top-0 left-0 right-0 z-50">
      {/* Top thin bar with phone number */}
      <div className="bg-taiyo-red py-1">
        <div className="container-taiyo flex justify-end items-center">
          <div className="flex items-center gap-4 text-white text-xs">
            <span>人気の部品</span>
            <span>パイプ加工</span>
            <span>企業情報</span>
            <a href="tel:0723612111" className="flex items-center gap-1 font-bold">
              <Phone size={12} />
              ************
            </a>
          </div>
        </div>
      </div>

      {/* Main header with single-row layout */}
      <div className="py-2 bg-white shadow-sm">
        <div className="container-taiyo flex items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex-shrink-0">
            <Image
              src="/images/logos/hr-logo.png"
              alt="寧波市北倫華日金属製品有限公司"
              width={70}
              height={20}
              priority
              className="h-auto"
            />
          </Link>

          {/* Desktop Navigation Menu - positioned next to logo */}
          <nav className="hidden lg:flex items-center gap-6 ml-8">
            <div className="group relative">
              <Link href="/advantage" className="text-sm font-medium text-gray-700 hover:text-taiyo-red transition-colors py-2">
                華日の強み
              </Link>
              <div className="absolute hidden group-hover:block bg-white border border-gray-200 shadow-lg p-3 z-[60] min-w-48 rounded top-full left-0">
                <Link href="/advantage" className="block py-1 px-2 text-sm hover:bg-gray-100 rounded">
                  華日の強みTOP
                </Link>
                <Link href="/advantage/development" className="block py-1 px-2 text-sm hover:bg-gray-100 rounded">
                  商品設計・開発
                </Link>
                <Link href="/advantage/oversea" className="block py-1 px-2 text-sm hover:bg-gray-100 rounded">
                  海外生産・調達
                </Link>
              </div>
            </div>

            <Link href="/services" className="text-sm font-medium text-gray-700 hover:text-taiyo-red transition-colors py-2">
              提案力
            </Link>

            <Link href="/manufacturer_function" className="text-sm font-medium text-gray-700 hover:text-taiyo-red transition-colors py-2">
              製造力
            </Link>

            <div className="group relative">
              <Link href="/technology" className="text-sm font-medium text-gray-700 hover:text-taiyo-red transition-colors py-2">
                調達力
              </Link>
              <div className="absolute hidden group-hover:block bg-white border border-gray-200 shadow-lg p-3 z-[60] min-w-48 rounded top-full left-0">
                <Link href="/technology" className="block py-1 px-2 text-sm hover:bg-gray-100 rounded">
                  調達力TOP
                </Link>
                <Link href="/technology/diecast" className="block py-1 px-2 text-sm hover:bg-gray-100 rounded">
                  ダイカスト
                </Link>
                <Link href="/technology/aluminum" className="block py-1 px-2 text-sm hover:bg-gray-100 rounded">
                  アルミ押し出し
                </Link>
                <Link href="/technology/lost_wax" className="block py-1 px-2 text-sm hover:bg-gray-100 rounded">
                  ロストワックス
                </Link>
                <Link href="/technology/sand_casting" className="block py-1 px-2 text-sm hover:bg-gray-100 rounded">
                  砂型鋳物
                </Link>
                <Link href="/technology/surface_treatment" className="block py-1 px-2 text-sm hover:bg-gray-100 rounded">
                  表面処理
                </Link>
                <Link href="/technology/unit" className="block py-1 px-2 text-sm hover:bg-gray-100 rounded">
                  ユニット組立
                </Link>
              </div>
            </div>

            <Link href="/product" className="text-sm font-medium text-gray-700 hover:text-taiyo-red transition-colors py-2">
              製作事例
            </Link>

            <Link href="/diecast-navi" className="text-sm font-medium text-gray-700 hover:text-taiyo-red transition-colors py-2">
              ダイカスト
            </Link>

            <Link href="/lostwax-navi" className="text-sm font-medium text-gray-700 hover:text-taiyo-red transition-colors py-2">
              ロストワックス
            </Link>

            <Link href="/scene" className="text-sm font-medium text-gray-700 hover:text-taiyo-red transition-colors py-2">
              こんなところに華日パーツ！
            </Link>
          </nav>

          {/* Right side action buttons */}
          <div className="flex items-center gap-2">
            {/* Desktop action buttons */}
            <div className="hidden lg:flex items-center gap-2">
              <Link href="/recruit" className="flex items-center">
                <Image
                  src="https://www.taiyoparts.co.jp/wp/wp-content/themes/taiyoparts/images/common/rec.png"
                  alt="採用情報"
                  width={70}
                  height={20}
                  className="hover:opacity-80 transition-opacity"
                />
              </Link>
              <Link href="/contact" className="flex items-center">
                <Image
                  src="https://www.taiyoparts.co.jp/wp/wp-content/themes/taiyoparts/images/common/inq.png"
                  alt="お問い合わせ"
                  width={70}
                  height={20}
                  className="hover:opacity-80 transition-opacity"
                />
              </Link>
            </div>

            {/* Mobile menu button */}
            <div className="lg:hidden flex items-center gap-2">
              <Link href="/contact" className="flex items-center">
                <Image
                  src="https://www.taiyoparts.co.jp/wp/wp-content/themes/taiyoparts/images/common/inq.png"
                  alt="お問い合わせ"
                  width={70}
                  height={20}
                  className="hover:opacity-80 transition-opacity"
                />
              </Link>
              <button
                onClick={() => setIsOpen(true)}
                className="p-2"
              >
                <Image
                  src="https://www.taiyoparts.co.jp/wp/wp-content/themes/taiyoparts/images/common/menu.png"
                  alt="メニューを開く"
                  width={30}
                  height={30}
                  className="hover:opacity-80 transition-opacity"
                />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetContent side="right" className="w-[80vw] sm:w-[385px] bg-white">
          <div className="flex justify-between items-center mb-6">
            <Link href="/" onClick={() => setIsOpen(false)}>
              <Image
                src="/images/logos/hr-logo.png"
                alt="寧波市北倫華日金属製品有限公司"
                width={70}
                height={20}
                className="h-auto"
              />
            </Link>
            <button onClick={() => setIsOpen(false)}>
              <Image
                src="https://www.taiyoparts.co.jp/wp/wp-content/themes/taiyoparts/images/common/close.png"
                alt="メニューを閉じる"
                width={24}
                height={24}
              />
            </button>
          </div>

          <nav className="flex flex-col gap-4">
            <div className="border-b pb-3">
              <Link href="/advantage" className="block py-2 font-bold text-base" onClick={() => setIsOpen(false)}>
                華日の強み
              </Link>
              <Link href="/advantage" className="block py-1 text-sm text-gray-600 ml-4" onClick={() => setIsOpen(false)}>
                華日の強みTOP
              </Link>
              <Link href="/advantage/development" className="block py-1 text-sm text-gray-600 ml-4" onClick={() => setIsOpen(false)}>
                商品設計・開発
              </Link>
              <Link href="/advantage/oversea" className="block py-1 text-sm text-gray-600 ml-4" onClick={() => setIsOpen(false)}>
                海外生産・調達
              </Link>
            </div>

            <Link href="/services" className="py-2 border-b font-bold text-base" onClick={() => setIsOpen(false)}>
              提案力
            </Link>

            <Link href="/manufacturer_function" className="py-2 border-b font-bold text-base" onClick={() => setIsOpen(false)}>
              製造力
            </Link>

            <div className="border-b pb-3">
              <Link href="/technology" className="block py-2 font-bold text-base" onClick={() => setIsOpen(false)}>
                調達力
              </Link>
              <Link href="/technology" className="block py-1 text-sm text-gray-600 ml-4" onClick={() => setIsOpen(false)}>
                調達力TOP
              </Link>
              <Link href="/technology/diecast" className="block py-1 text-sm text-gray-600 ml-4" onClick={() => setIsOpen(false)}>
                ダイカスト
              </Link>
              <Link href="/technology/aluminum" className="block py-1 text-sm text-gray-600 ml-4" onClick={() => setIsOpen(false)}>
                アルミ押し出し
              </Link>
              <Link href="/technology/lost_wax" className="block py-1 text-sm text-gray-600 ml-4" onClick={() => setIsOpen(false)}>
                ロストワックス
              </Link>
              <Link href="/technology/sand_casting" className="block py-1 text-sm text-gray-600 ml-4" onClick={() => setIsOpen(false)}>
                砂型鋳物
              </Link>
              <Link href="/technology/surface_treatment" className="block py-1 text-sm text-gray-600 ml-4" onClick={() => setIsOpen(false)}>
                表面処理
              </Link>
              <Link href="/technology/unit" className="block py-1 text-sm text-gray-600 ml-4" onClick={() => setIsOpen(false)}>
                ユニット組立
              </Link>
            </div>

            <Link href="/product" className="py-2 border-b font-bold text-base" onClick={() => setIsOpen(false)}>
              製作事例
            </Link>

            <Link href="/diecast-navi" className="py-2 border-b font-bold text-base" onClick={() => setIsOpen(false)}>
              ダイカスト
            </Link>

            <Link href="/lostwax-navi" className="py-2 border-b font-bold text-base" onClick={() => setIsOpen(false)}>
              ロストワックス
            </Link>

            <Link href="/scene" className="py-2 border-b font-bold text-base" onClick={() => setIsOpen(false)}>
              こんなところに華日パーツ！
            </Link>

            <Link href="/contact" className="py-2 border-b font-bold text-base" onClick={() => setIsOpen(false)}>
              お問い合わせ
            </Link>

            <Link href="/company" className="py-2 border-b font-bold text-base" onClick={() => setIsOpen(false)}>
              企業情報
            </Link>

            <Link href="/recruit" className="py-2 border-b font-bold text-base" onClick={() => setIsOpen(false)}>
              採用情報
            </Link>

            <Link href="/faq" className="py-2 border-b font-bold text-base" onClick={() => setIsOpen(false)}>
              よくあるご質問
            </Link>

            <Link href="/news" className="py-2 border-b font-bold text-base" onClick={() => setIsOpen(false)}>
              お知らせ
            </Link>

            <Link href="/blog" className="py-2 border-b font-bold text-base" onClick={() => setIsOpen(false)}>
              ブログ
            </Link>

            <div className="pt-4">
              <Link href="/privacy" className="text-sm text-gray-600" onClick={() => setIsOpen(false)}>
                プライバシーポリシー
              </Link>
            </div>
          </nav>
        </SheetContent>
      </Sheet>
    </header>
  )
}
