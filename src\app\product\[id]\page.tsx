import { Metadata } from "next"
import { notFound } from "next/navigation"
import Image from "next/image"
import Link from "next/link"
import { getAllProducts } from "@/lib/productUtils"

interface ProductDetailPageProps {
  params: {
    id: string
  }
}

export async function generateMetadata({ params }: ProductDetailPageProps): Promise<Metadata> {
  const products = getAllProducts()
  const product = products.find(p => p.id === params.id)
  
  if (!product) {
    return {
      title: "製品が見つかりません | 寧波市北倫華日金属製品有限公司"
    }
  }

  return {
    title: `${product.name} | 寧波市北倫華日金属製品有限公司`,
    description: `${product.category}の${product.name}の詳細情報をご覧ください。`,
  }
}

export default function ProductDetailPage({ params }: ProductDetailPageProps) {
  const products = getAllProducts()
  const product = products.find(p => p.id === params.id)

  if (!product) {
    notFound()
  }

  return (
    <div className="min-h-screen bg-gray-50 pt-24">
      <div className="container-taiyo py-8">
        {/* Breadcrumb */}
        <nav className="mb-6">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <Link href="/" className="hover:text-taiyo-red transition-colors">
              HOME
            </Link>
            <span>•</span>
            <Link href="/product" className="hover:text-taiyo-red transition-colors">
              製作事例
            </Link>
            <span>•</span>
            <span className="text-gray-900 font-medium">{product.name}</span>
          </div>
        </nav>

        {/* Product Detail */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 p-8">
            {/* Product Image */}
            <div className="relative aspect-square">
              <Image
                src={product.imagePath}
                alt={product.name}
                fill
                className="object-cover rounded-lg"
                sizes="(max-width: 1024px) 100vw, 50vw"
              />
            </div>

            {/* Product Information */}
            <div className="space-y-6">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-4">
                  {product.name}
                </h1>
                
                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm font-medium text-gray-600 mb-1">
                      [ カテゴリ ]
                    </h3>
                    <p className="text-lg text-gray-900">
                      {product.category}
                    </p>
                  </div>
                  
                  <div>
                    <h3 className="text-sm font-medium text-gray-600 mb-1">
                      [ 製品名 ]
                    </h3>
                    <p className="text-lg text-gray-900">
                      {product.name}
                    </p>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="space-y-4">
                <Link
                  href="/contact"
                  className="block w-full bg-taiyo-red text-white text-center py-3 px-6 rounded-md hover:bg-taiyo-red/90 transition-colors font-medium"
                >
                  この製品についてお問い合わせ
                </Link>
                
                <Link
                  href="/product"
                  className="block w-full border border-gray-300 text-gray-700 text-center py-3 px-6 rounded-md hover:bg-gray-50 transition-colors font-medium"
                >
                  製作事例一覧に戻る
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Related Products or Additional Info */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            お見積り・ご相談はこちら
          </h2>
          
          <div className="bg-white border border-gray-200 rounded-lg p-8 text-center">
            <div className="mb-6">
              <a 
                href="tel:0723612111" 
                className="text-3xl font-bold text-taiyo-red hover:text-taiyo-red/80 transition-colors"
              >
                ************
              </a>
              <p className="text-sm text-gray-600 mt-1">
                営業時間 9：00～17：00（土日祝 定休）
              </p>
            </div>
            
            <Link 
              href="/contact"
              className="inline-flex items-center bg-taiyo-red text-white px-8 py-3 rounded-md hover:bg-taiyo-red/90 transition-colors font-medium"
            >
              お問い合わせ
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
