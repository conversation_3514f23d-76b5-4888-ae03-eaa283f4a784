"use client"

import { useState } from "react"
import Image from "next/image"

interface ProductSearchProps {
  onSearch: (filters: SearchFilters) => void
  onReset: () => void
}

export interface SearchFilters {
  material: string
  processing: string
  finalProcess: string
}

export function ProductSearch({ onSearch, onReset }: ProductSearchProps) {
  const [filters, setFilters] = useState<SearchFilters>({
    material: "",
    processing: "",
    finalProcess: ""
  })

  const materials = [
    "アルミ", "亜鉛", "ステンレス", "鉄", "真鍮", "マグネシウム", 
    "樹脂", "ゴム", "銅", "その他"
  ]

  const processings = [
    "ダイカスト", "ロストワックス", "鋳物", "グラビティ―", "押し出し", "切削",
    "板金", "プレス", "パイプ加工", "溶接", "線材加工", "樹脂成形", "鍛造",
    "圧造（ヘッダー）", "MIM", "バネ", "その他"
  ]

  const finalProcesses = [
    "塗装", "メッキ", "アルマイト", "組み立て", "コーティング", 
    "電解研磨", "加工", "その他"
  ]

  const handleFilterChange = (type: keyof SearchFilters, value: string) => {
    const newFilters = { ...filters, [type]: value }
    setFilters(newFilters)
  }

  const handleSearch = () => {
    onSearch(filters)
  }

  const handleReset = () => {
    const resetFilters = { material: "", processing: "", finalProcess: "" }
    setFilters(resetFilters)
    onReset()
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 mb-8">
      {/* Header */}
      <div className="text-center mb-6">
        <div className="flex justify-center items-center mb-4">
          <Image
            src="https://www.taiyoparts.co.jp/wp/wp-content/themes/taiyoparts/images/common/parts_search2.png"
            alt="PARTS×SEARCH"
            width={200}
            height={40}
            className="h-auto"
          />
        </div>
        <p className="text-gray-700 text-sm leading-relaxed">
          どの様な部品の課題をお持ちでしょうか？<br />
          素材や加工法などから、類似の事例をご覧頂けます。
        </p>
      </div>

      {/* Search Form */}
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Material Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              ▼[材質]を選択
            </label>
            <select
              value={filters.material}
              onChange={(e) => handleFilterChange("material", e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-taiyo-red focus:border-taiyo-red"
            >
              <option value="">選択してください</option>
              {materials.map((material) => (
                <option key={material} value={material}>
                  {material}
                </option>
              ))}
            </select>
          </div>

          {/* Processing Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              ▼[加工]を選択
            </label>
            <select
              value={filters.processing}
              onChange={(e) => handleFilterChange("processing", e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-taiyo-red focus:border-taiyo-red"
            >
              <option value="">選択してください</option>
              {processings.map((processing) => (
                <option key={processing} value={processing}>
                  {processing}
                </option>
              ))}
            </select>
          </div>

          {/* Final Process Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              ▼[最終工程]を選択
            </label>
            <select
              value={filters.finalProcess}
              onChange={(e) => handleFilterChange("finalProcess", e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-taiyo-red focus:border-taiyo-red"
            >
              <option value="">選択してください</option>
              {finalProcesses.map((process) => (
                <option key={process} value={process}>
                  {process}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-center gap-4 pt-4">
          <button
            onClick={handleSearch}
            className="bg-taiyo-red text-white px-8 py-2 rounded-md hover:bg-taiyo-red/90 transition-colors font-medium"
          >
            検 索
          </button>
          <button
            onClick={handleReset}
            className="border border-gray-300 text-gray-700 px-8 py-2 rounded-md hover:bg-gray-50 transition-colors font-medium"
          >
            リセット
          </button>
        </div>
      </div>
    </div>
  )
}
