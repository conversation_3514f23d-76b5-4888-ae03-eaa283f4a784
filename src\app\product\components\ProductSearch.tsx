"use client"

import { useState } from "react"
import Image from "next/image"

interface ProductSearchProps {
  onSearch: (filters: SearchFilters) => void
  onReset: () => void
}

export interface SearchFilters {
  category: string
  keyword: string
  searchText: string
}

export function ProductSearch({ onSearch, onReset }: ProductSearchProps) {
  const [filters, setFilters] = useState<SearchFilters>({
    category: "",
    keyword: "",
    searchText: ""
  })

  // 实际的产品分类
  const categories = [
    "照明器具",
    "産業用油圧ドライブ",
    "産業用駆動装置",
    "自動化および通信電子機器",
    "自動車・オートバイ部品"
  ]

  // 常见的产品关键词（从实际产品名称中提取）
  const keywords = [
    "LED", "ランプ", "ライト", "シェル", "カバー", "ヒートシンク",
    "フィルター", "モーター", "ケース", "ドライブ", "リンクバー",
    "フランジ", "フレーム", "チェーンソー", "ベース",
    "ロボット", "サーボ", "クーラー", "ステー", "ボックス",
    "ミシン", "サーバー", "電気", "クリップ",
    "クラッチ", "トランスミッション", "オイルパン", "リアケース",
    "燃料", "電動", "エアコン", "分配", "ユニット", "ジョイント"
  ]

  const handleFilterChange = (type: keyof SearchFilters, value: string) => {
    const newFilters = { ...filters, [type]: value }
    setFilters(newFilters)
  }

  const handleSearch = () => {
    onSearch(filters)
  }

  const handleReset = () => {
    const resetFilters = { category: "", keyword: "", searchText: "" }
    setFilters(resetFilters)
    onReset()
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 mb-8">
      {/* Header */}
      <div className="text-center mb-6">
        <div className="flex justify-center items-center mb-4">
          <Image
            src="https://www.taiyoparts.co.jp/wp/wp-content/themes/taiyoparts/images/common/parts_search2.png"
            alt="PARTS×SEARCH"
            width={200}
            height={40}
            className="h-auto"
          />
        </div>
        <p className="text-gray-700 text-sm leading-relaxed">
          どの様な部品をお探しでしょうか？<br />
          分類やキーワードから、製品をお探しいただけます。
        </p>
      </div>

      {/* Search Form */}
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Category Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              ▼[分類]を選択
            </label>
            <select
              value={filters.category}
              onChange={(e) => handleFilterChange("category", e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-taiyo-red focus:border-taiyo-red"
            >
              <option value="">すべての分類</option>
              {categories.map((category) => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>
          </div>

          {/* Keyword Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              ▼[キーワード]を選択
            </label>
            <select
              value={filters.keyword}
              onChange={(e) => handleFilterChange("keyword", e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-taiyo-red focus:border-taiyo-red"
            >
              <option value="">すべてのキーワード</option>
              {keywords.map((keyword) => (
                <option key={keyword} value={keyword}>
                  {keyword}
                </option>
              ))}
            </select>
          </div>

          {/* Free Text Search */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              フリーテキスト検索
            </label>
            <input
              type="text"
              value={filters.searchText}
              onChange={(e) => handleFilterChange("searchText", e.target.value)}
              placeholder="製品名で検索..."
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-taiyo-red focus:border-taiyo-red"
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-center gap-4 pt-4">
          <button
            onClick={handleSearch}
            className="bg-taiyo-red text-white px-8 py-2 rounded-md hover:bg-taiyo-red/90 transition-colors font-medium"
          >
            検 索
          </button>
          <button
            onClick={handleReset}
            className="border border-gray-300 text-gray-700 px-8 py-2 rounded-md hover:bg-gray-50 transition-colors font-medium"
          >
            リセット
          </button>
        </div>
      </div>
    </div>
  )
}
