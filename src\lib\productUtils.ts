export interface Product {
  id: string
  name: string
  category: string
  imagePath: string
  fileName: string
}

export interface ProductCategory {
  name: string
  products: Product[]
}

// Static product data - in a real application, this would come from an API or database
export function getProductData(): ProductCategory[] {
  const categories: ProductCategory[] = [
    {
      name: '照明器具',
      products: [
        {
          id: '照明器具-LEDストリートランプ用シェル',
          name: 'LEDストリートランプ用シェル',
          category: '照明器具',
          imagePath: '/images/products/照明器具/LEDストリートランプ用シェル.png',
          fileName: 'LEDストリートランプ用シェル.png'
        },
        {
          id: '照明器具-LEDスポットライト',
          name: 'LEDスポットライト',
          category: '照明器具',
          imagePath: '/images/products/照明器具/LEDスポットライト.png',
          fileName: 'LEDスポットライト.png'
        },
        {
          id: '照明器具-LEDランプ用ヒートシンク',
          name: 'LEDランプ用ヒートシンク',
          category: '照明器具',
          imagePath: '/images/products/照明器具/LEDランプ用ヒートシンク.png',
          fileName: 'LEDランプ用ヒートシンク.png'
        },
        {
          id: '照明器具-LED工場・鉱山灯用シェル',
          name: 'LED工場・鉱山灯用シェル',
          category: '照明器具',
          imagePath: '/images/products/照明器具/LED工場・鉱山灯用シェル.png',
          fileName: 'LED工場・鉱山灯用シェル.png'
        },
        {
          id: '照明器具-ガーデンフロントランプカバー',
          name: 'ガーデンフロントランプカバー',
          category: '照明器具',
          imagePath: '/images/products/照明器具/ガーデンフロントランプカバー.png',
          fileName: 'ガーデンフロントランプカバー.png'
        }
      ]
    },
    {
      name: '産業用油圧ドライブ',
      products: [
        {
          id: '産業用油圧ドライブ-BKM155型フィルター用メインシェル',
          name: 'BKM155型フィルター用メインシェル',
          category: '産業用油圧ドライブ',
          imagePath: '/images/products/産業用油圧ドライブ/BKM155型フィルター用メインシェル.png',
          fileName: 'BKM155型フィルター用メインシェル.png'
        },
        {
          id: '産業用油圧ドライブ-モーターリンクバー',
          name: 'モーターリンクバー',
          category: '産業用油圧ドライブ',
          imagePath: '/images/products/産業用油圧ドライブ/モーターリンクバー.png',
          fileName: 'モーターリンクバー.png'
        },
        {
          id: '産業用油圧ドライブ-増容型モーターケース',
          name: '増容型モーターケース',
          category: '産業用油圧ドライブ',
          imagePath: '/images/products/産業用油圧ドライブ/増容型モーターケース.png',
          fileName: '増容型モーターケース.png'
        },
        {
          id: '産業用油圧ドライブ-油圧ドライブ用メインシェル',
          name: '油圧ドライブ用メインシェル',
          category: '産業用油圧ドライブ',
          imagePath: '/images/products/産業用油圧ドライブ/油圧ドライブ用メインシェル.png',
          fileName: '油圧ドライブ用メインシェル.png'
        },
        {
          id: '産業用油圧ドライブ-洗浄機用フランジリング',
          name: '洗浄機用フランジリング',
          category: '産業用油圧ドライブ',
          imagePath: '/images/products/産業用油圧ドライブ/洗浄機用フランジリング.png',
          fileName: '洗浄機用フランジリング.png'
        },
        {
          id: '産業用油圧ドライブ-草刈機フレーム',
          name: '草刈機フレーム',
          category: '産業用油圧ドライブ',
          imagePath: '/images/products/産業用油圧ドライブ/草刈機フレーム.png',
          fileName: '草刈機フレーム.png'
        }
      ]
    },
    {
      name: '産業用駆動装置',
      products: [
        {
          id: '産業用駆動装置-チェーンソー用ベース',
          name: 'チェーンソー用ベース',
          category: '産業用駆動装置',
          imagePath: '/images/products/産業用駆動装置/チェーンソー用ベース.png',
          fileName: 'チェーンソー用ベース.png'
        },
        {
          id: '産業用駆動装置-電動モーターカバー',
          name: '電動モーターカバー',
          category: '産業用駆動装置',
          imagePath: '/images/products/産業用駆動装置/電動モーターカバー.png',
          fileName: '電動モーターカバー.png'
        }
      ]
    },
    {
      name: '自動化および通信電子機器',
      products: [
        {
          id: '自動化および通信電子機器-フィルター',
          name: 'フィルター',
          category: '自動化および通信電子機器',
          imagePath: '/images/products/自動化および通信電子機器/フィルター.png',
          fileName: 'フィルター.png'
        },
        {
          id: '自動化および通信電子機器-ロボット用サーボクーラーステー',
          name: 'ロボット用サーボクーラーステー',
          category: '自動化および通信電子機器',
          imagePath: '/images/products/自動化および通信電子機器/ロボット用サーボクーラーステー.png',
          fileName: 'ロボット用サーボクーラーステー.png'
        },
        {
          id: '自動化および通信電子機器-信号分配ボックス',
          name: '信号分配ボックス',
          category: '自動化および通信電子機器',
          imagePath: '/images/products/自動化および通信電子機器/信号分配ボックス.png',
          fileName: '信号分配ボックス.png'
        },
        {
          id: '自動化および通信電子機器-産業用ミシン用ヒートシンクフレーム',
          name: '産業用ミシン用ヒートシンクフレーム',
          category: '自動化および通信電子機器',
          imagePath: '/images/products/自動化および通信電子機器/産業用ミシン用ヒートシンクフレーム.png',
          fileName: '産業用ミシン用ヒートシンクフレーム.png'
        },
        {
          id: '自動化および通信電子機器-自動制御サーバー用ヒートシンクステー',
          name: '自動制御サーバー用ヒートシンクステー',
          category: '自動化および通信電子機器',
          imagePath: '/images/products/自動化および通信電子機器/自動制御サーバー用ヒートシンクステー.png',
          fileName: '自動制御サーバー用ヒートシンクステー.png'
        },
        {
          id: '自動化および通信電子機器-電力用電気ラインクリップ',
          name: '電力用電気ラインクリップ',
          category: '自動化および通信電子機器',
          imagePath: '/images/products/自動化および通信電子機器/電力用電気ラインクリップ.png',
          fileName: '電力用電気ラインクリップ.png'
        }
      ]
    },
    {
      name: '自動車・オートバイ部品',
      products: [
        {
          id: '自動車・オートバイ部品-クラッチクーラー',
          name: 'クラッチクーラー',
          category: '自動車・オートバイ部品',
          imagePath: '/images/products/自動車・オートバイ部品/クラッチクーラー.png',
          fileName: 'クラッチクーラー.png'
        },
        {
          id: '自動車・オートバイ部品-クラッチヒートシンクカバー',
          name: 'クラッチヒートシンクカバー',
          category: '自動車・オートバイ部品',
          imagePath: '/images/products/自動車・オートバイ部品/クラッチヒートシンクカバー.png',
          fileName: 'クラッチヒートシンクカバー.png'
        },
        {
          id: '自動車・オートバイ部品-トランスミッションオイルパン',
          name: 'トランスミッションオイルパン',
          category: '自動車・オートバイ部品',
          imagePath: '/images/products/自動車・オートバイ部品/トランスミッションオイルパン.png',
          fileName: 'トランスミッションオイルパン.png'
        },
        {
          id: '自動車・オートバイ部品-トランスミッションリアケース',
          name: 'トランスミッションリアケース',
          category: '自動車・オートバイ部品',
          imagePath: '/images/products/自動車・オートバイ部品/トランスミッションリアケース.png',
          fileName: 'トランスミッションリアケース.png'
        },
        {
          id: '自動車・オートバイ部品-トランスミッションリアケース2',
          name: 'トランスミッションリアケース2',
          category: '自動車・オートバイ部品',
          imagePath: '/images/products/自動車・オートバイ部品/トランスミッションリアケース2.png',
          fileName: 'トランスミッションリアケース2.png'
        },
        {
          id: '自動車・オートバイ部品-燃料フィルター二重電動ケース',
          name: '燃料フィルター二重電動ケース',
          category: '自動車・オートバイ部品',
          imagePath: '/images/products/自動車・オートバイ部品/燃料フィルター二重電動ケース.png',
          fileName: '燃料フィルター二重電動ケース.png'
        },
        {
          id: '自動車・オートバイ部品-自動車クラッチ右側カバー',
          name: '自動車クラッチ右側カバー',
          category: '自動車・オートバイ部品',
          imagePath: '/images/products/自動車・オートバイ部品/自動車クラッチ右側カバー.png',
          fileName: '自動車クラッチ右側カバー.png'
        },
        {
          id: '自動車・オートバイ部品-自動車用エアコン分配ユニット',
          name: '自動車用エアコン分配ユニット',
          category: '自動車・オートバイ部品',
          imagePath: '/images/products/自動車・オートバイ部品/自動車用エアコン分配ユニット.png',
          fileName: '自動車用エアコン分配ユニット.png'
        },
        {
          id: '自動車・オートバイ部品-自動車用ジョイント',
          name: '自動車用ジョイント',
          category: '自動車・オートバイ部品',
          imagePath: '/images/products/自動車・オートバイ部品/自動車用ジョイント.png',
          fileName: '自動車用ジョイント.png'
        }
      ]
    },
    {
      name: '一般製品',
      products: [
        {
          id: 'root-p1',
          name: 'p1',
          category: '一般製品',
          imagePath: '/images/products/p1.png',
          fileName: 'p1.png'
        },
        {
          id: 'root-p2',
          name: 'p2',
          category: '一般製品',
          imagePath: '/images/products/p2.png',
          fileName: 'p2.png'
        },
        {
          id: 'root-p3',
          name: 'p3',
          category: '一般製品',
          imagePath: '/images/products/p3.png',
          fileName: 'p3.png'
        },
        {
          id: 'root-p4',
          name: 'p4',
          category: '一般製品',
          imagePath: '/images/products/p4.png',
          fileName: 'p4.png'
        },
        {
          id: 'root-p5',
          name: 'p5',
          category: '一般製品',
          imagePath: '/images/products/p5.png',
          fileName: 'p5.png'
        },
        {
          id: 'root-p6',
          name: 'p6',
          category: '一般製品',
          imagePath: '/images/products/p6.png',
          fileName: 'p6.png'
        },
        {
          id: 'root-p7',
          name: 'p7',
          category: '一般製品',
          imagePath: '/images/products/p7.png',
          fileName: 'p7.png'
        },
        {
          id: 'root-p8',
          name: 'p8',
          category: '一般製品',
          imagePath: '/images/products/p8.png',
          fileName: 'p8.png'
        },
        {
          id: 'root-p9',
          name: 'p9',
          category: '一般製品',
          imagePath: '/images/products/p9.png',
          fileName: 'p9.png'
        }
      ]
    }
  ]

  return categories
}

/**
 * Get all products as a flat array
 */
export function getAllProducts(): Product[] {
  const categories = getProductData()
  return categories.flatMap(category => category.products)
}

/**
 * Get products by category
 */
export function getProductsByCategory(categoryName: string): Product[] {
  const categories = getProductData()
  const category = categories.find(cat => cat.name === categoryName)
  return category ? category.products : []
}

/**
 * Get unique categories
 */
export function getCategories(): string[] {
  const categories = getProductData()
  return categories.map(category => category.name)
}
