import fs from 'fs'
import path from 'path'

export interface Product {
  id: string
  name: string
  category: string
  imagePath: string
  fileName: string
}

export interface ProductCategory {
  name: string
  products: Product[]
}

/**
 * Get all product categories and their products from the file system
 */
export function getProductData(): ProductCategory[] {
  const productsDir = path.join(process.cwd(), 'public', 'images', 'products')
  
  try {
    const items = fs.readdirSync(productsDir, { withFileTypes: true })
    const categories: ProductCategory[] = []
    
    // Process directories (categories)
    const directories = items.filter(item => item.isDirectory())
    
    for (const dir of directories) {
      const categoryName = dir.name
      const categoryPath = path.join(productsDir, categoryName)
      
      try {
        const files = fs.readdirSync(categoryPath)
        const products: Product[] = []
        
        // Process image files in the category
        const imageFiles = files.filter(file => {
          const ext = path.extname(file).toLowerCase()
          return ['.jpg', '.jpeg', '.png', '.gif', '.webp'].includes(ext)
        })
        
        for (const file of imageFiles) {
          const fileName = path.parse(file).name
          const product: Product = {
            id: `${categoryName}-${fileName}`.replace(/[^a-zA-Z0-9-_]/g, '-'),
            name: fileName,
            category: categoryName,
            imagePath: `/images/products/${categoryName}/${file}`,
            fileName: file
          }
          products.push(product)
        }
        
        if (products.length > 0) {
          categories.push({
            name: categoryName,
            products: products
          })
        }
      } catch (error) {
        console.warn(`Error reading category ${categoryName}:`, error)
      }
    }
    
    // Also process individual files in the root products directory
    const rootFiles = items.filter(item => item.isFile())
    const rootImageFiles = rootFiles.filter(file => {
      const ext = path.extname(file.name).toLowerCase()
      return ['.jpg', '.jpeg', '.png', '.gif', '.webp'].includes(ext)
    })
    
    if (rootImageFiles.length > 0) {
      const rootProducts: Product[] = []
      
      for (const file of rootImageFiles) {
        const fileName = path.parse(file.name).name
        const product: Product = {
          id: `root-${fileName}`.replace(/[^a-zA-Z0-9-_]/g, '-'),
          name: fileName,
          category: '一般製品', // General products
          imagePath: `/images/products/${file.name}`,
          fileName: file.name
        }
        rootProducts.push(product)
      }
      
      categories.push({
        name: '一般製品',
        products: rootProducts
      })
    }
    
    return categories
  } catch (error) {
    console.error('Error reading products directory:', error)
    return []
  }
}

/**
 * Get all products as a flat array
 */
export function getAllProducts(): Product[] {
  const categories = getProductData()
  return categories.flatMap(category => category.products)
}

/**
 * Get products by category
 */
export function getProductsByCategory(categoryName: string): Product[] {
  const categories = getProductData()
  const category = categories.find(cat => cat.name === categoryName)
  return category ? category.products : []
}

/**
 * Get unique categories
 */
export function getCategories(): string[] {
  const categories = getProductData()
  return categories.map(category => category.name)
}
