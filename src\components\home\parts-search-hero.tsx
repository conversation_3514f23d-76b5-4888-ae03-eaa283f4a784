"use client"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"

export function PartsSearchHero() {
  const [material, setMaterial] = useState("")
  const [processing, setProcessing] = useState("")
  const [finalProcess, setFinalProcess] = useState("")

  const materials = [
    "アルミ", "亜鉛", "ステンレス", "鉄", "真鍮", "マグネシウム", 
    "樹脂", "ゴム", "銅", "その他"
  ]

  const processings = [
    "ダイカスト", "ロストワックス", "鋳物", "グラビティ―", "押し出し", "切削",
    "板金", "プレス", "パイプ加工", "溶接", "線材加工", "樹脂成形", "鍛造",
    "圧造（ヘッダー）", "MIM", "バネ", "その他"
  ]

  const finalProcesses = [
    "塗装", "メッキ", "アルマイト", "組み立て", "コーティング", 
    "電解研磨", "加工", "その他"
  ]

  const handleSearch = () => {
    // Handle search logic here
    console.log("Search:", { material, processing, finalProcess })
  }

  return (
    <div
      className="relative py-12 border-b border-gray-200 bg-cover bg-center bg-no-repeat"
      style={{
        backgroundImage: "url('https://www.taiyoparts.co.jp/wp/wp-content/themes/taiyoparts/images/common/parts_search2.png')"
      }}
    >
      {/* Background overlay for better text readability */}
      <div className="absolute inset-0 bg-white/80"></div>

      <div className="container-taiyo relative z-10">
        {/* Parts Search Header */}
        <div className="text-center mb-8">
          <div className="flex justify-center items-center mb-6">
            <Image
              src="https://www.taiyoparts.co.jp/wp/wp-content/themes/taiyoparts/images/common/parts_search.png"
              alt="PARTS×SEARCH"
              width={250}
              height={75}
              className="h-auto"
            />
          </div>
          <p className="text-base text-gray-700 mb-2 font-medium">
            どの様な部品の課題をお持ちでしょうか？
          </p>
          <p className="text-base text-gray-700">
            素材や加工法などから、類似の事例をご覧頂けます。
          </p>
        </div>

        {/* Search Form */}
        <div className="max-w-5xl mx-auto bg-white p-8 rounded-lg shadow-lg">
          <div className="flex flex-col md:flex-row gap-6 items-end">
            {/* Material Selection */}
            <div className="flex-1">
              <label className="block text-sm font-bold text-gray-700 mb-3">
                ▼[材質]を選択
              </label>
              <select
                value={material}
                onChange={(e) => setMaterial(e.target.value)}
                className="w-full p-4 border-2 border-gray-300 rounded-sm bg-white text-sm focus:border-taiyo-red focus:outline-none transition-colors"
              >
                <option value="">材質を選択してください</option>
                {materials.map((mat) => (
                  <option key={mat} value={mat}>
                    {mat}
                  </option>
                ))}
              </select>
              {material && (
                <button
                  onClick={() => setMaterial("")}
                  className="mt-2 text-xs text-taiyo-red hover:text-taiyo-red/80 font-medium"
                >
                  × クリア
                </button>
              )}
            </div>

            {/* Processing Selection */}
            <div className="flex-1">
              <label className="block text-sm font-bold text-gray-700 mb-3">
                ▼[加工]を選択
              </label>
              <select
                value={processing}
                onChange={(e) => setProcessing(e.target.value)}
                className="w-full p-4 border-2 border-gray-300 rounded-sm bg-white text-sm focus:border-taiyo-red focus:outline-none transition-colors"
              >
                <option value="">加工方法を選択してください</option>
                {processings.map((proc) => (
                  <option key={proc} value={proc}>
                    {proc}
                  </option>
                ))}
              </select>
              {processing && (
                <button
                  onClick={() => setProcessing("")}
                  className="mt-2 text-xs text-taiyo-red hover:text-taiyo-red/80 font-medium"
                >
                  × クリア
                </button>
              )}
            </div>

            {/* Final Process Selection */}
            <div className="flex-1">
              <label className="block text-sm font-bold text-gray-700 mb-3">
                ▼[最終工程]を選択
              </label>
              <select
                value={finalProcess}
                onChange={(e) => setFinalProcess(e.target.value)}
                className="w-full p-4 border-2 border-gray-300 rounded-sm bg-white text-sm focus:border-taiyo-red focus:outline-none transition-colors"
              >
                <option value="">最終工程を選択してください</option>
                {finalProcesses.map((proc) => (
                  <option key={proc} value={proc}>
                    {proc}
                  </option>
                ))}
              </select>
              {finalProcess && (
                <button
                  onClick={() => setFinalProcess("")}
                  className="mt-2 text-xs text-taiyo-red hover:text-taiyo-red/80 font-medium"
                >
                  × クリア
                </button>
              )}
            </div>

            {/* Search Button */}
            <div className="flex-shrink-0">
              <button
                onClick={handleSearch}
                className="bg-taiyo-red text-white px-12 py-4 rounded-sm hover:bg-taiyo-red/90 transition-colors font-bold text-lg shadow-lg"
              >
                検 索
              </button>
            </div>
          </div>
        </div>

        {/* Quick Action Buttons */}
        <div className="flex flex-col sm:flex-row justify-center gap-6 mt-12">
          <Link
            href="/contact"
            className="flex items-center justify-center gap-3 bg-white border-2 border-taiyo-red text-taiyo-red px-8 py-4 rounded-sm hover:bg-taiyo-red hover:text-white transition-colors font-bold text-lg shadow-lg"
          >
            <Image
              src="https://www.taiyoparts.co.jp/wp/wp-content/themes/taiyoparts/images/common/mail.png"
              alt="お問い合わせ"
              width={24}
              height={24}
            />
            お問い合わせ
          </Link>
          <Link
            href="/recruit"
            className="flex items-center justify-center gap-3 bg-white border-2 border-taiyo-red text-taiyo-red px-8 py-4 rounded-sm hover:bg-taiyo-red hover:text-white transition-colors font-bold text-lg shadow-lg"
          >
            <Image
              src="https://www.taiyoparts.co.jp/wp/wp-content/themes/taiyoparts/images/common/icon_human.png"
              alt="採用情報"
              width={24}
              height={24}
            />
            採用情報
          </Link>
        </div>
      </div>
    </div>
  )
}
