import Image from "next/image"
import Link from "next/link"
import { Product } from "@/lib/productUtils"

interface ProductCardProps {
  product: Product
}

export function ProductCard({ product }: ProductCardProps) {
  return (
    <div className="group relative bg-white border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow duration-300">
      <Link href={`/product/${product.id}`} className="block">
        {/* Product Image */}
        <div className="relative aspect-[4/3] overflow-hidden">
          <Image
            src={product.imagePath}
            alt={product.name}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />

          {/* Product Title Overlay - positioned in center with bold text */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="bg-black bg-opacity-60 px-4 py-2 rounded">
              <h3 className="text-white font-bold text-base text-center leading-tight">
                {product.name}
              </h3>
            </div>
          </div>
        </div>

        {/* Product Details - Black background section */}
        <div className="bg-black text-white p-3">
          <div className="space-y-1">
            {/* Material/Category info */}
            <div className="text-xs">
              <span className="text-gray-300">[ 材　質 ]</span>
            </div>
            <div className="text-sm font-medium">
              {product.category}
            </div>

            {/* Processing info */}
            <div className="text-xs mt-2">
              <span className="text-gray-300">[ 加　工 ]</span>
            </div>
            <div className="text-sm font-medium">
              {product.name}
            </div>
          </div>
        </div>
      </Link>
    </div>
  )
}
